import InvoiceForm from "@/components/invoice-form";
import InvoiceList from "@/components/invoice-list";
import Sidebar from "@/components/sidebar";
import { Navigate, Route, Routes } from "react-router-dom";
import Dashboard from "./dashboard";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="flex">
        <Sidebar />
        <main className="flex-1 ml-64 p-8">
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/create-invoice" element={<InvoiceForm />} />
            <Route path="/edit-invoice/:id" element={<InvoiceForm />} />
            {/* <Route path="/customers" element={<CustomerManagement />} /> */}
            <Route path="/invoices" element={<InvoiceList />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default Index;
